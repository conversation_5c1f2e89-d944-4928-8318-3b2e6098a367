<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="終了テンプレート" />
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-xl font-semibold">終了テンプレート管理</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            チャットボットの会話終了時に使用するテンプレートを管理します
          </p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus" @click="createTemplate">
          新規テンプレート作成
        </UButton>
      </div>

      <!-- Templates List -->
      <div class="space-y-4">
        <UCard 
          v-for="template in templates" 
          :key="template.id"
          class="hover:shadow-md transition-shadow duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-3">
                <UIcon name="i-heroicons-document-text" class="text-blue-500" />
                <h3 class="font-semibold">{{ template.name }}</h3>
                <UBadge 
                  :color="template.isActive ? 'green' : 'gray'"
                  variant="soft"
                  size="sm"
                >
                  {{ template.isActive ? '有効' : '無効' }}
                </UBadge>
              </div>
              
              <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-3">
                <p class="text-sm whitespace-pre-wrap">{{ template.content }}</p>
              </div>
              
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span>カテゴリ: {{ template.category }}</span>
                <span>作成日: {{ formatDate(template.createdAt) }}</span>
                <span>更新日: {{ formatDate(template.updatedAt) }}</span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2 ml-4">
              <UButton 
                color="gray" 
                variant="soft" 
                size="sm"
                icon="i-heroicons-pencil"
                @click="editTemplate(template)"
              >
                編集
              </UButton>
              <UButton 
                :color="template.isActive ? 'red' : 'green'"
                variant="soft" 
                size="sm"
                @click="toggleTemplate(template)"
              >
                {{ template.isActive ? '無効化' : '有効化' }}
              </UButton>
              <UDropdown :items="getTemplateActions(template)">
                <UButton 
                  color="gray" 
                  variant="ghost" 
                  icon="i-heroicons-ellipsis-vertical"
                  size="sm"
                />
              </UDropdown>
            </div>
          </div>
        </UCard>

        <!-- Empty State -->
        <div v-if="templates.length === 0" class="text-center py-12">
          <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
            終了テンプレートがありません
          </h3>
          <p class="text-gray-500 mb-4">
            最初の終了テンプレートを作成してください
          </p>
          <UButton color="primary" @click="createTemplate">
            テンプレートを作成
          </UButton>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingTemplate ? 'テンプレート編集' : '新規テンプレート作成' }}
          </h3>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="テンプレート名" required>
            <UInput v-model="templateForm.name" placeholder="例: 問題解決完了" />
          </UFormGroup>
          
          <UFormGroup label="カテゴリ" required>
            <USelect 
              v-model="templateForm.category" 
              :options="categoryOptions"
              placeholder="カテゴリを選択"
            />
          </UFormGroup>
          
          <UFormGroup label="テンプレート内容" required>
            <UTextarea 
              v-model="templateForm.content" 
              placeholder="お疲れ様でした。ご質問は解決されましたでしょうか？&#10;他にもご不明な点がございましたら、いつでもお声かけください。"
              :rows="6"
            />
            <template #help>
              <span class="text-xs text-gray-500">
                改行は自動的に反映されます。変数 {userName} を使用してユーザー名を挿入できます。
              </span>
            </template>
          </UFormGroup>
          
          <UFormGroup label="使用条件">
            <UTextarea 
              v-model="templateForm.condition" 
              placeholder="このテンプレートを使用する条件を記述してください（任意）"
              :rows="2"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton color="primary" @click="saveTemplate" :loading="saving">
              {{ editingTemplate ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-end-template",
  middleware: ["permissions"],
});

const showModal = ref(false)
const saving = ref(false)
const editingTemplate = ref(null)

const categoryOptions = [
  { label: '問題解決完了', value: 'resolved' },
  { label: '情報提供完了', value: 'information' },
  { label: '案内完了', value: 'guidance' },
  { label: '営業時間外', value: 'off-hours' },
  { label: 'エラー対応', value: 'error' },
  { label: 'その他', value: 'other' }
]

const templates = ref([
  {
    id: 1,
    name: '問題解決完了',
    category: 'resolved',
    content: 'お疲れ様でした。ご質問は解決されましたでしょうか？\n他にもご不明な点がございましたら、いつでもお声かけください。\n\n本日はありがとうございました。',
    condition: '問題が解決された場合',
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 2,
    name: '情報提供完了',
    category: 'information',
    content: '必要な情報をお伝えできましたでしょうか？\n\n追加でご質問がございましたら、お気軽にお声かけください。',
    condition: '情報提供が完了した場合',
    isActive: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 3,
    name: '営業時間外対応',
    category: 'off-hours',
    content: '申し訳ございませんが、現在営業時間外です。\n営業時間は平日9:00-18:00となっております。\n\n営業時間内に改めてお問い合わせください。',
    condition: '営業時間外の場合',
    isActive: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  }
])

const templateForm = reactive({
  name: '',
  category: '',
  content: '',
  condition: ''
})

const formatDate = (date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const getTemplateActions = (template) => [
  [{
    label: '複製',
    icon: 'i-heroicons-document-duplicate',
    click: () => duplicateTemplate(template)
  }],
  [{
    label: '削除',
    icon: 'i-heroicons-trash',
    click: () => deleteTemplate(template)
  }]
]

const createTemplate = () => {
  editingTemplate.value = null
  Object.assign(templateForm, {
    name: '',
    category: '',
    content: '',
    condition: ''
  })
  showModal.value = true
}

const editTemplate = (template) => {
  editingTemplate.value = template
  Object.assign(templateForm, {
    name: template.name,
    category: template.category,
    content: template.content,
    condition: template.condition
  })
  showModal.value = true
}

const saveTemplate = async () => {
  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingTemplate.value) {
      // Update existing template
      Object.assign(editingTemplate.value, templateForm)
      editingTemplate.value.updatedAt = new Date()
    } else {
      // Create new template
      templates.value.push({
        id: Date.now(),
        ...templateForm,
        isActive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    closeModal()
    
    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `テンプレートを${editingTemplate.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'テンプレートの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingTemplate.value = null
}

const toggleTemplate = async (template) => {
  template.isActive = !template.isActive
  template.updatedAt = new Date()
  
  const toast = useToast()
  toast.add({
    title: template.isActive ? 'テンプレートを有効化しました' : 'テンプレートを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateTemplate = (template) => {
  const newTemplate = {
    ...template,
    id: Date.now(),
    name: `${template.name} (コピー)`,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  templates.value.push(newTemplate)
  
  const toast = useToast()
  toast.add({
    title: 'テンプレートを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteTemplate = (template) => {
  const index = templates.value.findIndex(t => t.id === template.id)
  if (index > -1) {
    templates.value.splice(index, 1)
    
    const toast = useToast()
    toast.add({
      title: 'テンプレートを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
