<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="シナリオ管理" />
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-xl font-semibold">チャットボットシナリオ</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            チャットボットの会話フローを管理します
          </p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus" @click="createScenario">
          新規シナリオ作成
        </UButton>
      </div>

      <!-- Scenarios List -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <UCard 
          v-for="scenario in scenarios" 
          :key="scenario.id"
          class="hover:shadow-lg transition-shadow duration-200"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-document-text" class="text-blue-500" />
                <h3 class="font-semibold">{{ scenario.name }}</h3>
              </div>
              <UDropdown :items="getScenarioActions(scenario)">
                <UButton 
                  color="gray" 
                  variant="ghost" 
                  icon="i-heroicons-ellipsis-vertical"
                  size="sm"
                />
              </UDropdown>
            </div>
          </template>
          
          <div class="space-y-3">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ scenario.description }}
            </p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>ステップ数: {{ scenario.steps }}</span>
              <span :class="scenario.isActive ? 'text-green-500' : 'text-gray-400'">
                {{ scenario.isActive ? '有効' : '無効' }}
              </span>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>作成日: {{ formatDate(scenario.createdAt) }}</span>
              <span>更新日: {{ formatDate(scenario.updatedAt) }}</span>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-between">
              <UButton 
                color="gray" 
                variant="soft" 
                size="sm"
                @click="editScenario(scenario)"
              >
                編集
              </UButton>
              <UButton 
                :color="scenario.isActive ? 'red' : 'green'"
                variant="soft" 
                size="sm"
                @click="toggleScenario(scenario)"
              >
                {{ scenario.isActive ? '無効化' : '有効化' }}
              </UButton>
            </div>
          </template>
        </UCard>

        <!-- Empty State -->
        <div v-if="scenarios.length === 0" class="col-span-full">
          <div class="text-center py-12">
            <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              シナリオがありません
            </h3>
            <p class="text-gray-500 mb-4">
              最初のチャットボットシナリオを作成してください
            </p>
            <UButton color="primary" @click="createScenario">
              シナリオを作成
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingScenario ? 'シナリオ編集' : '新規シナリオ作成' }}
          </h3>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="シナリオ名" required>
            <UInput v-model="scenarioForm.name" placeholder="例: 初回問い合わせ対応" />
          </UFormGroup>
          <UFormGroup label="説明">
            <UTextarea 
              v-model="scenarioForm.description" 
              placeholder="このシナリオの説明を入力してください"
              :rows="3"
            />
          </UFormGroup>
          <UFormGroup label="トリガーキーワード">
            <UInput 
              v-model="scenarioForm.triggerKeyword" 
              placeholder="例: こんにちは, はじめまして"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton color="primary" @click="saveScenario" :loading="saving">
              {{ editingScenario ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-scenario",
  middleware: ["permissions"],
});

const showModal = ref(false)
const saving = ref(false)
const editingScenario = ref(null)

const scenarios = ref([
  {
    id: 1,
    name: '初回問い合わせ対応',
    description: '初めて問い合わせをするユーザー向けの基本的な対応フロー',
    steps: 5,
    isActive: true,
    triggerKeyword: 'こんにちは',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 2,
    name: 'FAQ自動応答',
    description: 'よくある質問に対する自動応答シナリオ',
    steps: 8,
    isActive: true,
    triggerKeyword: '質問',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 3,
    name: '営業時間外対応',
    description: '営業時間外のユーザー対応フロー',
    steps: 3,
    isActive: false,
    triggerKeyword: '',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  }
])

const scenarioForm = reactive({
  name: '',
  description: '',
  triggerKeyword: ''
})

const formatDate = (date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const getScenarioActions = (scenario) => [
  [{
    label: '編集',
    icon: 'i-heroicons-pencil',
    click: () => editScenario(scenario)
  }],
  [{
    label: '複製',
    icon: 'i-heroicons-document-duplicate',
    click: () => duplicateScenario(scenario)
  }],
  [{
    label: '削除',
    icon: 'i-heroicons-trash',
    click: () => deleteScenario(scenario)
  }]
]

const createScenario = () => {
  editingScenario.value = null
  Object.assign(scenarioForm, {
    name: '',
    description: '',
    triggerKeyword: ''
  })
  showModal.value = true
}

const editScenario = (scenario) => {
  editingScenario.value = scenario
  Object.assign(scenarioForm, {
    name: scenario.name,
    description: scenario.description,
    triggerKeyword: scenario.triggerKeyword
  })
  showModal.value = true
}

const saveScenario = async () => {
  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingScenario.value) {
      // Update existing scenario
      Object.assign(editingScenario.value, scenarioForm)
      editingScenario.value.updatedAt = new Date()
    } else {
      // Create new scenario
      scenarios.value.push({
        id: Date.now(),
        ...scenarioForm,
        steps: 1,
        isActive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    closeModal()
    
    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `シナリオを${editingScenario.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'シナリオの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingScenario.value = null
}

const toggleScenario = async (scenario) => {
  scenario.isActive = !scenario.isActive
  scenario.updatedAt = new Date()
  
  const toast = useToast()
  toast.add({
    title: scenario.isActive ? 'シナリオを有効化しました' : 'シナリオを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateScenario = (scenario) => {
  const newScenario = {
    ...scenario,
    id: Date.now(),
    name: `${scenario.name} (コピー)`,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  scenarios.value.push(newScenario)
  
  const toast = useToast()
  toast.add({
    title: 'シナリオを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteScenario = (scenario) => {
  const index = scenarios.value.findIndex(s => s.id === scenario.id)
  if (index > -1) {
    scenarios.value.splice(index, 1)
    
    const toast = useToast()
    toast.add({
      title: 'シナリオを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
