<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="チャットボット設定" />
    <div class="p-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Settings -->
        <UCard>
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-chat-bubble-left-right" class="text-blue-500" />
              <h3 class="text-lg font-semibold">基本設定</h3>
            </div>
          </template>
          <div class="space-y-4">
            <UFormGroup label="チャットボット名" required>
              <UInput 
                v-model="chatbotConfig.name" 
                placeholder="例: サポートボット"
              />
            </UFormGroup>
            <UFormGroup label="ウェルカムメッセージ" required>
              <UTextarea 
                v-model="chatbotConfig.welcomeMessage"
                placeholder="こんにちは！何かお困りのことはありませんか？"
                :rows="3"
              />
            </UFormGroup>
            <UFormGroup label="チャットボットの有効化">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  チャットボット機能を有効にします
                </span>
                <UToggle
                  v-model="chatbotConfig.isEnabled"
                  on-icon="i-heroicons-check-20-solid"
                  off-icon="i-heroicons-x-mark-20-solid"
                />
              </div>
            </UFormGroup>
          </div>
        </UCard>

        <!-- Auto Response Settings -->
        <UCard>
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-clock" class="text-green-500" />
              <h3 class="text-lg font-semibold">自動応答設定</h3>
            </div>
          </template>
          <div class="space-y-4">
            <UFormGroup label="自動応答の有効化">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  一定時間後に自動でメッセージを送信します
                </span>
                <UToggle
                  v-model="chatbotConfig.autoResponse"
                  on-icon="i-heroicons-check-20-solid"
                  off-icon="i-heroicons-x-mark-20-solid"
                />
              </div>
            </UFormGroup>
            <UFormGroup 
              v-if="chatbotConfig.autoResponse"
              label="応答待機時間（秒）" 
              required
            >
              <UInput 
                v-model="chatbotConfig.responseDelay" 
                type="number"
                min="1"
                max="300"
                placeholder="30"
              />
              <template #help>
                <span class="text-xs text-gray-500">
                  1秒から300秒（5分）まで設定可能です
                </span>
              </template>
            </UFormGroup>
            <UFormGroup label="営業時間外メッセージ">
              <UTextarea 
                v-model="chatbotConfig.offHoursMessage"
                placeholder="申し訳ございませんが、現在営業時間外です。"
                :rows="2"
              />
            </UFormGroup>
          </div>
        </UCard>
      </div>

      <!-- Action Buttons -->
      <div class="mt-6 flex justify-end space-x-3">
        <UButton color="gray" variant="soft" @click="resetSettings">
          リセット
        </UButton>
        <UButton color="primary" @click="saveSettings" :loading="loading">
          保存
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-chatbot",
  middleware: ["permissions"],
});

const loading = ref(false)

const chatbotConfig = reactive({
  name: '',
  welcomeMessage: '',
  isEnabled: false,
  autoResponse: false,
  responseDelay: 30,
  offHoursMessage: ''
})

const saveSettings = async () => {
  loading.value = true
  try {
    // TODO: Implement API call to save chatbot settings
    console.log('Saving chatbot settings:', chatbotConfig)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: 'チャットボット設定を保存しました。',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: '設定の保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

const resetSettings = () => {
  Object.assign(chatbotConfig, {
    name: '',
    welcomeMessage: '',
    isEnabled: false,
    autoResponse: false,
    responseDelay: 30,
    offHoursMessage: ''
  })
}

// Load settings on mount
onMounted(() => {
  // TODO: Load chatbot settings from API
  console.log('Loading chatbot settings...')
})
</script>
