<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="チャットボット一覧" />
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-xl font-semibold">チャットボット管理</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            登録されているチャットボットの一覧と管理
          </p>
        </div>
        <UButton color="primary" icon="i-heroicons-plus" @click="createChatbot">
          新規チャットボット作成
        </UButton>
      </div>

      <!-- Chatbot List -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <UCard
          v-for="chatbot in chatbots"
          :key="chatbot.id"
          class="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          @click="viewChatbotDetail(chatbot)"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-2xl text-blue-500" />
                <div>
                  <h3 class="font-semibold text-lg">{{ chatbot.name }}</h3>
                  <p class="text-sm text-gray-500">ID: {{ chatbot.id }}</p>
                </div>
              </div>
              <UDropdown :items="getChatbotActions(chatbot)" @click.stop>
                <UButton
                  color="gray"
                  variant="ghost"
                  icon="i-heroicons-ellipsis-vertical"
                  size="sm"
                />
              </UDropdown>
            </div>
          </template>

          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">説明</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ chatbot.description || '説明なし' }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">ウェルカムメッセージ</label>
              <div class="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                {{ chatbot.welcomeMessage }}
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <UBadge
                  :color="chatbot.isActive ? 'green' : 'gray'"
                  variant="soft"
                  size="sm"
                >
                  {{ chatbot.isActive ? '有効' : '無効' }}
                </UBadge>
                <UBadge
                  :color="chatbot.autoResponse ? 'blue' : 'gray'"
                  variant="soft"
                  size="sm"
                >
                  {{ chatbot.autoResponse ? '自動応答ON' : '自動応答OFF' }}
                </UBadge>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <span class="font-medium">シナリオ数:</span>
                <span class="ml-1">{{ chatbot.scenarioCount }}</span>
              </div>
              <div>
                <span class="font-medium">テンプレート数:</span>
                <span class="ml-1">{{ chatbot.templateCount }}</span>
              </div>
              <div>
                <span class="font-medium">作成日:</span>
                <span class="ml-1">{{ formatDate(chatbot.createdAt) }}</span>
              </div>
              <div>
                <span class="font-medium">更新日:</span>
                <span class="ml-1">{{ formatDate(chatbot.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-between items-center">
              <UButton
                color="gray"
                variant="soft"
                size="sm"
                icon="i-heroicons-pencil"
                @click.stop="editChatbot(chatbot)"
              >
                編集
              </UButton>
              <UButton
                :color="chatbot.isActive ? 'red' : 'green'"
                variant="soft"
                size="sm"
                @click.stop="toggleChatbot(chatbot)"
              >
                {{ chatbot.isActive ? '無効化' : '有効化' }}
              </UButton>
            </div>
          </template>
        </UCard>

        <!-- Empty State -->
        <div v-if="chatbots.length === 0" class="col-span-full">
          <div class="text-center py-12">
            <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              チャットボットがありません
            </h3>
            <p class="text-gray-500 mb-4">
              最初のチャットボットを作成してください
            </p>
            <UButton color="primary" @click="createChatbot">
              チャットボットを作成
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

// Mock data for chatbots
const chatbots = ref([
  {
    id: 'bot_001',
    name: 'サポートボット',
    description: '一般的なサポート業務を担当するチャットボット',
    welcomeMessage: 'こんにちは！何かお困りのことはありませんか？',
    isActive: true,
    autoResponse: true,
    responseDelay: 30,
    scenarioCount: 5,
    templateCount: 8,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'bot_002',
    name: 'FAQ自動応答ボット',
    description: 'よくある質問に自動で回答するチャットボット',
    welcomeMessage: 'よくある質問にお答えします。どのようなことでお困りですか？',
    isActive: true,
    autoResponse: false,
    responseDelay: 60,
    scenarioCount: 12,
    templateCount: 15,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'bot_003',
    name: '営業時間外対応ボット',
    description: '営業時間外の問い合わせに対応するチャットボット',
    welcomeMessage: '申し訳ございませんが、現在営業時間外です。',
    isActive: false,
    autoResponse: true,
    responseDelay: 10,
    scenarioCount: 3,
    templateCount: 5,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  }
])

const formatDate = (date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

const getChatbotActions = (chatbot) => [
  [{
    label: '詳細表示',
    icon: 'i-heroicons-eye',
    click: () => viewChatbotDetail(chatbot)
  }],
  [{
    label: '複製',
    icon: 'i-heroicons-document-duplicate',
    click: () => duplicateChatbot(chatbot)
  }],
  [{
    label: '削除',
    icon: 'i-heroicons-trash',
    click: () => deleteChatbot(chatbot)
  }]
]

const createChatbot = () => {
  // Navigate to create chatbot page
  navigateTo('/app/chatbot/create')
}

const viewChatbotDetail = (chatbot) => {
  // Navigate to chatbot detail page
  navigateTo(`/app/chatbot/detail/${chatbot.id}`)
}

const editChatbot = (chatbot) => {
  // Navigate to edit chatbot page
  navigateTo(`/app/chatbot/edit/${chatbot.id}`)
}

const toggleChatbot = async (chatbot) => {
  chatbot.isActive = !chatbot.isActive
  chatbot.updatedAt = new Date()

  const toast = useToast()
  toast.add({
    title: chatbot.isActive ? 'チャットボットを有効化しました' : 'チャットボットを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateChatbot = (chatbot) => {
  const newChatbot = {
    ...chatbot,
    id: `bot_${Date.now()}`,
    name: `${chatbot.name} (コピー)`,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  chatbots.value.push(newChatbot)

  const toast = useToast()
  toast.add({
    title: 'チャットボットを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteChatbot = (chatbot) => {
  const index = chatbots.value.findIndex(c => c.id === chatbot.id)
  if (index > -1) {
    chatbots.value.splice(index, 1)

    const toast = useToast()
    toast.add({
      title: 'チャットボットを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
