No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,UI/UX,レスポンシブデザイン,デスクトップ表示,デスクトップ環境で適切に表示されること,"1. デスクトップブラウザ（1920x1080）でアクセス
2. 全ページが適切に表示されることを確認
3. ナビゲーションメニューが正しく表示されることを確認
4. コンテンツが読みやすく配置されていることを確認",デスクトップ対応,
2,UI/UX,レスポンシブデザイン,タブレット表示,タブレット環境で適切に表示されること,"1. タブレット（768x1024）でアクセス
2. レイアウトがタブレット用に調整されることを確認
3. タッチ操作が正常に動作することを確認
4. 文字サイズが適切であることを確認",タブレット対応,
3,UI/UX,レスポンシブデザイン,スマートフォン表示,スマートフォン環境で適切に表示されること,"1. スマートフォン（375x667）でアクセス
2. レイアウトがモバイル用に調整されることを確認
3. ハンバーガーメニューが表示されることを確認
4. スワイプ操作が正常に動作することを確認",モバイル対応,
4,UI/UX,ナビゲーション,メインナビゲーション,メインナビゲーションが直感的に操作できること,"1. AppNavigationコンポーネントの動作を確認
2. 各メニュー項目をクリック
3. 適切なページに遷移することを確認
4. アクティブ状態が正しく表示されることを確認
5. 権限に応じてメニューが表示/非表示されることを確認",ナビゲーション設計,
5,UI/UX,ナビゲーション,サブナビゲーション,サブナビゲーションが正常に動作すること,"1. AppSubNavigationコンポーネントの動作を確認
2. サブメニューの展開・折りたたみを確認
3. ミニ表示モードの切り替えを確認
4. サブメニュー項目の選択状態を確認",サブナビゲーション,
6,UI/UX,ナビゲーション,パンくずナビゲーション,パンくずナビゲーションが正しく表示されること,"1. 深い階層のページにアクセス
2. パンくずナビゲーションが表示されることを確認
3. 各階層のリンクが正しく動作することを確認
4. 現在位置が明確に示されることを確認",パンくず表示,
7,UI/UX,フォーム,入力バリデーション,フォーム入力時のバリデーションが適切に動作すること,"1. 各種フォームで必須項目を空のまま送信
2. バリデーションエラーが表示されることを確認
3. エラーメッセージが分かりやすいことを確認
4. エラー箇所が視覚的に分かりやすいことを確認",バリデーション機能,
8,UI/UX,フォーム,リアルタイムバリデーション,入力中のリアルタイムバリデーションが動作すること,"1. フォーム入力中にリアルタイムでバリデーションが実行されることを確認
2. 無効な入力時に即座にエラーが表示されることを確認
3. 有効な入力時にエラーが消えることを確認",リアルタイム検証,
9,UI/UX,フォーム,フォーム送信,フォーム送信時の処理が適切に動作すること,"1. フォームに有効なデータを入力
2. 送信ボタンをクリック
3. 送信中の状態が表示されることを確認
4. 送信完了時に成功メッセージが表示されることを確認
5. 送信失敗時にエラーメッセージが表示されることを確認",送信処理,
10,UI/UX,モーダル・ダイアログ,モーダル表示,モーダルダイアログが正しく表示されること,"1. モーダルを開くボタンをクリック
2. モーダルが正しく表示されることを確認
3. 背景がオーバーレイされることを確認
4. モーダル外をクリックして閉じることを確認
5. ESCキーで閉じることを確認",モーダル機能,
11,UI/UX,モーダル・ダイアログ,確認ダイアログ,確認ダイアログが適切に動作すること,"1. 削除等の重要な操作を実行
2. 確認ダイアログが表示されることを確認
3. キャンセルボタンで操作が中止されることを確認
4. 確定ボタンで操作が実行されることを確認",確認機能,
12,UI/UX,テーブル・リスト,データ表示,テーブルでのデータ表示が適切に動作すること,"1. データテーブルが正しく表示されることを確認
2. 列ヘッダーが適切に表示されることを確認
3. データが正しく整列されていることを確認
4. 空データ時の表示が適切であることを確認",テーブル表示,
13,UI/UX,テーブル・リスト,ソート機能,テーブルのソート機能が正常に動作すること,"1. 列ヘッダーをクリック
2. データが昇順でソートされることを確認
3. 再度クリックして降順でソートされることを確認
4. ソート状態が視覚的に分かることを確認",ソート機能,
14,UI/UX,テーブル・リスト,ページネーション,ページネーション機能が正常に動作すること,"1. 大量データでページネーションが表示されることを確認
2. 次ページボタンで次のページに移動することを確認
3. ページ番号をクリックして該当ページに移動することを確認
4. 表示件数の変更が正しく動作することを確認",ページング機能,
15,UI/UX,検索・フィルタ,検索機能,検索機能が正常に動作すること,"1. 検索フィールドにキーワードを入力
2. 検索ボタンをクリック
3. 該当するデータのみが表示されることを確認
4. 検索条件をクリアして全件表示に戻ることを確認",検索機能,
16,UI/UX,検索・フィルタ,フィルタ機能,フィルタ機能が正常に動作すること,"1. フィルタ条件を設定
2. フィルタを適用
3. 条件に合致するデータのみが表示されることを確認
4. 複数条件でのフィルタが正しく動作することを確認",フィルタ機能,
17,UI/UX,通知・メッセージ,成功通知,成功時の通知が適切に表示されること,"1. 正常な操作を実行
2. 成功通知が表示されることを確認
3. 通知メッセージが分かりやすいことを確認
4. 通知が自動的に消えることを確認",成功通知,
18,UI/UX,通知・メッセージ,エラー通知,エラー時の通知が適切に表示されること,"1. エラーが発生する操作を実行
2. エラー通知が表示されることを確認
3. エラーメッセージが分かりやすいことを確認
4. 解決方法が示されることを確認",エラー通知,
19,UI/UX,通知・メッセージ,警告通知,警告時の通知が適切に表示されること,"1. 注意が必要な操作を実行
2. 警告通知が表示されることを確認
3. 警告内容が明確であることを確認
4. 適切な色分けがされていることを確認",警告通知,
20,UI/UX,ローディング・状態表示,ローディング表示,ローディング状態が適切に表示されること,"1. 時間のかかる処理を実行
2. ローディングインジケーターが表示されることを確認
3. 処理完了時にローディングが消えることを確認
4. ローディング中は操作が無効化されることを確認",ローディング表示,
21,UI/UX,ローディング・状態表示,プログレス表示,進捗状況が適切に表示されること,"1. 段階的な処理を実行
2. プログレスバーが表示されることを確認
3. 進捗率が正しく更新されることを確認
4. 完了時に100%になることを確認",プログレス表示,
22,UI/UX,アクセシビリティ,キーボード操作,キーボードのみで操作できること,"1. マウスを使用せずにキーボードのみで操作
2. Tabキーでフォーカス移動ができることを確認
3. Enterキーでボタンが押せることを確認
4. 矢印キーでメニュー操作ができることを確認
5. ESCキーでモーダルが閉じることを確認",キーボードアクセシビリティ,
23,UI/UX,アクセシビリティ,フォーカス表示,フォーカス状態が視覚的に分かること,"1. Tabキーでフォーカスを移動
2. フォーカスされた要素が明確に分かることを確認
3. フォーカスリングが適切に表示されることを確認
4. カスタムフォーカススタイルが適用されていることを確認",フォーカス表示,
24,UI/UX,アクセシビリティ,色覚対応,色覚に配慮した表示になっていること,"1. 色覚シミュレーターで表示を確認
2. 色だけでなく形や文字でも情報が伝わることを確認
3. 十分なコントラスト比が確保されていることを確認",色覚アクセシビリティ,
25,UI/UX,アクセシビリティ,スクリーンリーダー対応,スクリーンリーダーで適切に読み上げられること,"1. スクリーンリーダーを有効にして操作
2. 画面内容が適切に読み上げられることを確認
3. フォーム項目のラベルが読み上げられることを確認
4. ボタンの機能が理解できることを確認
5. 見出し構造が適切であることを確認",スクリーンリーダー対応,
26,UI/UX,多言語対応,言語切り替え,言語切り替えが正常に動作すること,"1. 言語設定メニューにアクセス
2. 日本語を選択
3. 画面表示が日本語になることを確認
4. 英語に切り替え
5. 画面表示が英語になることを確認
6. ポルトガル語に切り替え
7. 画面表示がポルトガル語になることを確認",i18n機能,
27,UI/UX,多言語対応,文字表示,多言語文字が正しく表示されること,"1. 日本語、英語、ポルトガル語の文字を含むコンテンツを表示
2. 文字化けが発生しないことを確認
3. フォントが適切に適用されていることを確認
4. 文字の配置が適切であることを確認",文字エンコーディング,
28,UI/UX,テーマ・スタイル,ダークモード,ダークモードが正常に動作すること,"1. テーマ切り替えボタンをクリック
2. ダークモードに切り替わることを確認
3. 全ての要素が適切に表示されることを確認
4. ライトモードに戻すことができることを確認",ダークモード対応,
29,UI/UX,テーマ・スタイル,カスタムテーマ,カスタマー別テーマが適用されること,"1. テーマ設定が有効なカスタマーでアクセス
2. カスタムカラーが適用されることを確認
3. ロゴが適切に表示されることを確認
4. ブランディングが一貫していることを確認",カスタムテーマ,
30,統合機能,LINE連携,LINE認証,LINE認証が正常に動作すること,"1. LINE設定が完了したカスタマーでアクセス
2. LINEログインボタンをクリック
3. LINE認証画面が表示されることを確認
4. 認証完了後にシステムにログインできることを確認",LINE認証連携,
31,統合機能,LINE連携,メッセージ送受信,LINEでのメッセージ送受信が正常に動作すること,"1. LINE設定完了後、LINEアプリからメッセージを送信
2. システム側でメッセージを受信することを確認
3. カウンセラーが返信
4. LINEアプリでメッセージを受信することを確認
5. リアルタイムで同期されることを確認",LINE メッセージ連携,
32,統合機能,LINE連携,LIFF アプリ,LIFF アプリが正常に動作すること,"1. LIFF設定が完了したカスタマーでアクセス
2. LINEアプリ内でLIFFアプリを開く
3. チャット画面が表示されることを確認
4. メッセージの送受信が正常に動作することを確認
5. ファイル送信機能が動作することを確認",LIFF連携,
33,統合機能,LINE連携,Webhook処理,LINE Webhookが正常に処理されること,"1. LINE側からWebhookイベントを送信
2. システム側でイベントを受信することを確認
3. 適切な処理が実行されることを確認
4. レスポンスが正しく返されることを確認",Webhook処理,
34,統合機能,メール連携,メール送信,メール送信機能が正常に動作すること,"1. メール送信が必要な操作を実行
2. メールが正常に送信されることを確認
3. メール内容が正しいことを確認
4. 送信エラー時の処理が適切であることを確認",メール送信機能,
35,統合機能,メール連携,メールテンプレート,メールテンプレートが正しく適用されること,"1. 各種メールテンプレートを確認
2. 変数が正しく置換されることを確認
3. HTMLメールが適切に表示されることを確認
4. テキストメールが適切に表示されることを確認",メールテンプレート,
36,統合機能,外部API,API認証,外部API認証が正常に動作すること,"1. 外部API設定を確認
2. 認証情報が正しく設定されていることを確認
3. API接続テストを実行
4. 認証が成功することを確認",API認証,
37,統合機能,外部API,データ同期,外部システムとのデータ同期が正常に動作すること,"1. 外部システムからデータを取得
2. データが正しく同期されることを確認
3. データの整合性が保たれることを確認
4. 同期エラー時の処理が適切であることを確認",データ同期,
38,統合機能,ファイル管理,ファイルアップロード,ファイルアップロード機能が正常に動作すること,"1. ファイル選択ボタンをクリック
2. ファイルを選択
3. アップロードが正常に完了することを確認
4. ファイルサイズ制限が適切に動作することを確認
5. 許可されていないファイル形式が拒否されることを確認",ファイルアップロード,
39,統合機能,ファイル管理,ファイルダウンロード,ファイルダウンロード機能が正常に動作すること,"1. ダウンロード対象ファイルを選択
2. ダウンロードボタンをクリック
3. ファイルが正常にダウンロードされることを確認
4. ファイル内容が正しいことを確認",ファイルダウンロード,
40,統合機能,データエクスポート,CSV エクスポート,CSV形式でのデータエクスポートが正常に動作すること,"1. エクスポート対象データを選択
2. CSV形式を選択
3. エクスポートを実行
4. CSVファイルがダウンロードされることを確認
5. ファイル内容が正しいことを確認",CSV エクスポート,
41,統合機能,データエクスポート,Excel エクスポート,Excel形式でのデータエクスポートが正常に動作すること,"1. エクスポート対象データを選択
2. Excel形式を選択
3. エクスポートを実行
4. Excelファイルがダウンロードされることを確認
5. ファイル内容が正しいことを確認",Excel エクスポート,
42,統合機能,データインポート,CSV インポート,CSV形式でのデータインポートが正常に動作すること,"1. インポート用CSVファイルを準備
2. インポート機能にアクセス
3. CSVファイルを選択
4. インポートを実行
5. データが正しく取り込まれることを確認",CSV インポート,
43,統合機能,バックアップ・復旧,データバックアップ,データバックアップが正常に実行されること,"1. バックアップ機能にアクセス
2. バックアップ対象を選択
3. バックアップを実行
4. バックアップファイルが作成されることを確認
5. バックアップ内容が正しいことを確認",データバックアップ,
44,統合機能,バックアップ・復旧,データ復旧,データ復旧が正常に実行されること,"1. バックアップファイルを選択
2. 復旧機能にアクセス
3. 復旧を実行
4. データが正しく復旧されることを確認
5. システムが正常に動作することを確認",データ復旧,
45,パフォーマンス,ページ読み込み,初期表示速度,各ページの初期表示が適切な時間で完了すること,"1. 各主要ページにアクセス
2. ページ読み込み時間を測定
3. 3秒以内に表示されることを確認
4. First Contentful Paint が1.5秒以内であることを確認",パフォーマンス要件,
46,パフォーマンス,ページ読み込み,リソース最適化,リソースが適切に最適化されていること,"1. ブラウザの開発者ツールでリソースを確認
2. 画像が適切に圧縮されていることを確認
3. CSS/JSが最小化されていることを確認
4. 不要なリソースが読み込まれていないことを確認",リソース最適化,
47,パフォーマンス,データ処理,大量データ処理,大量データの処理が適切に動作すること,"1. 大量のケースデータ（1000件以上）を作成
2. 一覧表示の動作を確認
3. 検索・フィルタ機能の動作を確認
4. ページネーションの動作を確認
5. 応答時間が許容範囲内であることを確認",スケーラビリティ,
48,パフォーマンス,リアルタイム通信,同時接続,複数ユーザーの同時接続が正常に動作すること,"1. 複数のカウンセラーが同時にログイン
2. 同時にチャット機能を使用
3. メッセージの送受信が正常に動作することを確認
4. システムの応答性を確認
5. メモリ使用量を監視",同時接続性能,
49,パフォーマンス,メモリ使用量,メモリリーク,メモリリークが発生しないこと,"1. 長時間の連続使用を実行
2. メモリ使用量を監視
3. メモリリークが発生しないことを確認
4. ガベージコレクションが適切に動作することを確認",メモリ管理,
50,セキュリティ,認証・認可,権限チェック,適切な権限チェックが動作すること,"1. 各権限レベルのユーザーでログイン
2. アクセス可能なページが権限に応じて制限されることを確認
3. 権限のないページにアクセスした際にエラーが表示されることを確認
4. API呼び出し時の権限チェックが動作することを確認",権限ベースアクセス制御,
