# Customer Account Modal Feature Update

## 概要 (Overview)

このアップデートでは、CustomerAccountModalコンポーネントに「利用可能な機能」(Available Features)の管理機能を追加し、特にチャットボット機能の設定を強化しました。

## 主な変更点 (Main Changes)

### 1. 利用可能な機能の拡張 (Extended Available Features)

#### 追加された機能 (Added Features):
- **チャットボット (Chatbot)** - 自動応答チャットボット
- **テキストテンプレート (TextTemplate)** - 定型文テンプレート  
- **ケース管理 (Case)** - 相談ケース管理
- **タグ機能 (Tag)** - ケースタグ管理
- **相談者管理 (Counselee)** - 相談者情報管理
- **相談期間 (CounselingTerm)** - 相談期間設定
- **セグメント配信 (Segment)** - セグメント別メッセージ配信
- **エクスポート (Export)** - データエクスポート
- **アンケート (Survey)** - アンケート機能
- **アンケート結果 (SurveyResult)** - アンケート結果表示
- **ウィザード結果 (WizardResult)** - ウィザード結果表示

### 2. チャットボット設定機能 (Chatbot Configuration)

チャットボット機能が選択された場合、専用の設定セクションが表示されます：

#### 基本設定 (Basic Settings):
- **チャットボット名** - ボットの表示名
- **ウェルカムメッセージ** - 初回接触時のメッセージ

#### 自動応答設定 (Auto Response Settings):
- **自動応答の有効化** - 自動応答機能のON/OFF
- **応答待機時間** - 自動応答までの待機時間（1-300秒）

#### 高度な設定 (Advanced Settings):
- **営業時間外メッセージ** - 営業時間外の自動応答
- **キーワード自動応答** - 特定キーワードに対する自動応答設定

### 3. UI/UX改善 (UI/UX Improvements)

- **グリッドレイアウト**: 機能選択を3列グリッドで表示
- **選択状態の可視化**: 選択済み機能をバッジで表示
- **説明文の追加**: 各機能の説明を追加
- **アイコンの統一**: 各機能に適切なアイコンを設定

## ファイル変更 (File Changes)

### 新規作成 (New Files):
- `components/admin/customerAccount/ChatbotSettings.vue` - チャットボット設定専用コンポーネント
- `pages/test-modal.vue` - テスト用ページ
- `FEATURE_UPDATE_README.md` - この説明書

### 変更されたファイル (Modified Files):

#### 1. `composables/useConstants.ts`
- `featureList`を拡張し、全ての利用可能な機能を追加
- 各機能にアイコンと説明を設定

#### 2. `types/enums.d.ts`
- `FeatureList`型エイリアスを追加

#### 3. `types/customers.ts`
- `ChatbotConfig`インターフェースを追加
- `Customer`インターフェースに`chatbot`プロパティを追加

#### 4. `components/admin/customerAccount/CustomerAccountModal.vue`
- 機能選択UIを改善（3列グリッド、バッジ表示）
- チャットボット設定セクションを追加
- `initialState`に`featureList`と`chatbot`を追加
- `getFeatureTitle`メソッドを追加

## 使用方法 (Usage)

### 1. 機能の選択
1. CustomerAccountModalを開く
2. 「利用可能な機能」セクションで必要な機能をクリック
3. 選択された機能は青いボーダーで表示される
4. 選択済み機能は下部にバッジで表示される

### 2. チャットボット設定
1. 「チャットボット」機能を選択
2. 専用の設定セクションが表示される
3. 基本設定、自動応答設定、高度な設定を入力
4. キーワード応答は「+」ボタンで追加可能

### 3. テスト
テスト用ページ（`/test-modal`）でモーダルの動作を確認できます。

## 技術仕様 (Technical Specifications)

### Types:
```typescript
interface ChatbotConfig {
  name: string;
  welcomeMessage: string;
  autoResponse: boolean;
  responseDelay: number;
  offHoursMessage?: string;
  keywordResponses?: Array<{
    keyword: string;
    response: string;
  }>;
}
```

### Feature List:
```typescript
enum CustomerFeature {
  TextTemplate = "TextTemplate",
  Case = "Case", 
  Tag = "Tag",
  Counselee = "Counselee",
  CounselingTerm = "CounselingTerm",
  Segment = "Segment",
  Export = "Export",
  Survey = "Survey",
  SurveyResult = "SurveyResult",
  WizardResult = "WizardResult",
  Wizard = "Wizard",
  Chatbot = "Chatbot",
}
```

## 4. チャットボットメニューの追加 (Chatbot Menu Addition)

AppNavigationにチャットボットメニューを追加し、以下のサブメニューを実装しました：

### サブメニュー構成:
- **チャットボット** (`/app/chatbot/chatbot`) - 基本設定
- **シナリオ** (`/app/chatbot/scenario`) - 会話フロー管理
- **終了テンプレート** (`/app/chatbot/end-template`) - 終了時メッセージ管理
- **ログ** (`/app/chatbot/logs`) - 動作ログ確認

### 各ページの機能:

#### チャットボット設定 (`chatbot.vue`):
- 基本設定（名前、ウェルカムメッセージ、有効化）
- 自動応答設定（有効化、待機時間、営業時間外メッセージ）

#### シナリオ管理 (`scenario.vue`):
- シナリオの作成、編集、削除
- シナリオの有効化/無効化
- トリガーキーワード設定
- シナリオの複製機能

#### 終了テンプレート (`end-template.vue`):
- 終了時メッセージテンプレートの管理
- カテゴリ別分類（問題解決完了、情報提供完了など）
- テンプレートの有効化/無効化
- 変数挿入機能（{userName}など）

#### ログ管理 (`logs.vue`):
- チャットボット動作ログの表示
- フィルタリング機能（期間、レベル、ユーザーID、キーワード）
- ログ詳細表示
- エクスポート機能

## 今後の拡張予定 (Future Enhancements)

1. **機能別詳細設定**: 各機能に専用の設定画面を追加
2. **権限管理**: 機能ごとのアクセス権限設定
3. **使用状況分析**: 機能の使用状況レポート
4. **テンプレート機能**: 機能セットのテンプレート化
5. **チャットボットAI連携**: 外部AI APIとの連携機能
6. **シナリオビルダー**: ドラッグ&ドロップでのシナリオ作成
7. **A/Bテスト機能**: 複数シナリオの効果測定

## 注意事項 (Notes)

- チャットボット機能は選択時のみ設定セクションが表示されます
- 機能の選択状態はリアルタイムで更新されます
- バックエンドAPIとの連携は別途実装が必要です
- チャットボットメニューは`CustomerFeature.Chatbot`が有効な場合のみ表示されます
- 各サブページには適切な権限チェックが実装されています
