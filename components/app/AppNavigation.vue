<template>
  <aside
    class="fixed pt-0 border-l border-gray-700 dark:border-gray-700 top-0 left-20 z-30 w-24 h-screen -translate-x-full bg-gray-800 text-gray-100 dark:bg-gray-800 md:translate-x-0 text-center transition-all duration-200"
    :class="{
      'left-20': !isHideAppNavigation,
      '!-left-3.5 shadow-2xl': isHideAppNavigation,
    }"
  >
    <div class="flex flex-col overflow-y-auto pt-2 pb-4 h-full items-center space-y-4">
      <template v-for="item in navigations" :key="item.to">
        <AppNavigationItem v-bind="item" :is-active="currentPage === item.name" />
      </template>
    </div>
  </aside>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppCustomersStore } from "~/stores/app/customers";
import { CustomerFeature } from "@/types/enums.d";
import type { Customer } from "~/types";
const appUIStore = useAppUIStore();
const appCustomersStore = useAppCustomersStore();
const { avaiableFeatures, currentCustomer } = storeToRefs(appCustomersStore);
const { isHideAppNavigation } = storeToRefs(appUIStore);
const userPermissions = usePermissions();
const route = useRoute();
const currentPage = computed(() => route.meta.navigationPage || route.name);

const navigations = computed(() => {
  return [
    {
      name: "app",
      icon: "i-heroicons-home-solid",
      label: "ホーム",
      to: "/app",
      show: userPermissions.value.includes("read:app"),
    },
    {
      name: "app-chats",
      icon: "i-heroicons-chat-bubble-left-right-solid",
      label: "相談集中",
      to: "/app/chats",
      show: userPermissions.value.includes("read:app-chats"),
    },

    {
      name: "app-surveys",
      icon: "i-heroicons-clipboard-document-list-solid",
      label: "アンケート",
      to: "/app/surveys",
      show: userPermissions.value.includes("read:app-surveys"),
    },
    {
      name: "app-wizards",
      icon: "i-clarity-flow-chart-line",
      label: "ウィザード",
      to: "/app/wizards",
      show:
        userPermissions.value.includes("read:app-wizards") &&
        avaiableFeatures.value?.includes(CustomerFeature.Wizard),
    },
    {
      name: "app-reports",
      icon: "i-heroicons-document-chart-bar-solid",
      label: "レポート",
      to: "/app/reports",
      show: userPermissions.value.includes("read:app-reports"),
    },

    {
      name: "app-settings",
      icon: "i-heroicons-cog-8-tooth-solid",
      label: "設定",
      to: "/app/settings",
      show: userPermissions.value.includes("read:app-settings"),
    },
  ].filter((item) => item.show);
});
watch(currentCustomer, (customer: Customer, oldCustomer: Customer) => {
  if (customer && customer.customerId !== oldCustomer?.customerId) {
    const hasPermission = navigations.value.find(
      (item) => item.name === route.name && item.show
    );
    if (!hasPermission) {
      nextTick(() => {
        navigateTo("/app?customerId=" + route.query.customerId);
        console.log("🚀 ~ hasPermission:", hasPermission);
      });
    }
  }
});
</script>
